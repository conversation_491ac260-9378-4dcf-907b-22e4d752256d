import {
  Accordion,
  Alert,
  Autocomplete,
  Breadcrumbs,
  Button,
  CapsuleTab,
  Checkbox,
  Chip,
  DateInput,
  FloatButton,
  IconButton,
  Input,
  NavBar,
  Option,
  Pagination,
  ProductCard,
  Radio,
  RadioGroup,
  Select,
  Sidebar,
  SortingIcon,
  Switch,
  Tab,
  TabPanel,
  Tabs,
  TabsList,
  Typography,
  UploadBox,
} from "@apollo/ui/legacy"
import { Heart, Home, Plus, Setting, Shop, Shopping, User } from "@design-systems/apollo-icons"

const legacyComponents = [
  // INPUTS CATEGORY
  {
    title: "Button",
    href: "@design-systems∕apollo-ui/Components/Inputs/Button",
    description: "Primary action component with multiple variants and sizes",
    keywords: [
      "button",
      "action",
      "click",
      "primary",
      "danger",
      "solid",
      "outline",
      "plain",
    ],
    component: (
      <>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Button variant="solid">Solid</Button>
          <Button variant="outline">Outline</Button>
          <Button variant="plain">Plain</Button>
        </div>
      </>
    ),
  },
  {
    title: "Icon Button",
    href: "@design-systems∕apollo-ui/Components/Utilities/IconButton",
    description: "Button component specifically for icons",
    keywords: ["icon", "button", "action", "circular", "square"],
    component: (
      <>
        <div style={{ display: "flex", gap: "8px", alignItems: "center" }}>
          <IconButton>
            <Heart size={20} />
          </IconButton>
          <IconButton>
            <Setting size={20} />
          </IconButton>
        </div>
      </>
    ),
  },
  {
    title: "Float Button",
    href: "@design-systems∕apollo-ui/Components/Utilities/FloatButton",
    description: "Floating action button for primary actions",
    keywords: ["float", "button", "fab", "floating", "action", "primary"],
    component: (
      <>
        <FloatButton icon={<Plus size={20} />} label="Add" />
      </>
    ),
  },
  {
    title: "Input",
    href: "@design-systems∕apollo-ui/Components/Inputs/Input",
    description: "Text input field with validation and helper text support",
    keywords: ["input", "text", "field", "form", "validation"],
    component: (
      <>
        <Input placeholder="Enter text..." style={{ width: "200px" }} />
      </>
    ),
  },
  {
    title: "Checkbox",
    href: "@design-systems∕apollo-ui/Components/Inputs/Checkbox",
    description:
      "Binary choice input with checked, unchecked, and indeterminate states",
    keywords: ["checkbox", "check", "selection", "binary", "form"],
    component: (
      <>
        <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
          <Checkbox checked label="Checked" />
          <Checkbox label="Unchecked" />
          <Checkbox indeterminate label="Indeterminate" />
        </div>
      </>
    ),
  },
  {
    title: "Radio",
    href: "@design-systems∕apollo-ui/Components/Inputs/Radio",
    description: "Single choice selection from a group of options",
    keywords: ["radio", "selection", "choice", "group", "form"],
    component: (
      <>
        <RadioGroup value="option1" onChange={() => {}}>
          <Radio value="option1">Option 1</Radio>
          <Radio value="option2">Option 2</Radio>
        </RadioGroup>
      </>
    ),
  },
  {
    title: "Select",
    href: "@design-systems∕apollo-ui/Components/Inputs/Select",
    description:
      "Dropdown selection component with search and multi-select support",
    keywords: ["select", "dropdown", "options", "search", "multi-select"],
    component: (
      <>
        <Select placeholder="Choose option..." style={{ width: "200px" }}>
          <Option value="1">Option 1</Option>
          <Option value="2">Option 2</Option>
        </Select>
      </>
    ),
  },
  {
    title: "Switch",
    href: "@design-systems∕apollo-ui/Components/Inputs/Switch",
    description: "Toggle switch for binary settings and preferences",
    keywords: ["switch", "toggle", "binary", "settings", "on", "off"],
    component: (
      <>
        <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
          <Switch checked />
          <Switch />
        </div>
      </>
    ),
  },
  {
    title: "Date Input",
    href: "@design-systems∕apollo-ui/Components/Inputs/DateInput",
    description: "Date picker input with calendar interface",
    keywords: ["date", "picker", "calendar", "input", "time"],
    component: (
      <>
        <div style={{ width: "200px" }}>
          <DateInput placeholder="Select date..." />
        </div>
      </>
    ),
  },
  {
    title: "Autocomplete",
    href: "@design-systems∕apollo-ui/Components/Inputs/Autocomplete",
    description: "Input with autocomplete suggestions and filtering",
    keywords: ["autocomplete", "search", "suggestions", "filter", "input"],
    component: (
      <>
        <div style={{ width: "200px" }}>
          <Autocomplete
            placeholder="Search..."
            options={[
              { label: "Apple", value: "apple" },
              { label: "Banana", value: "banana" },
            ]}
          />
        </div>
      </>
    ),
  },
  {
    title: "Upload Box",
    href: "@design-systems∕apollo-ui/Components/Inputs/UploadBox",
    description: "File upload component with drag and drop support",
    keywords: ["upload", "file", "drag", "drop", "attachment"],
    component: <UploadBox />,
  },

  // DATA DISPLAY CATEGORY
  {
    title: "Typography",
    href: "@design-systems∕apollo-ui/Components/Data Display/Typography",
    description: "Text component with semantic levels and styling options",
    keywords: ["typography", "text", "heading", "body", "semantic"],
    component: (
      <>
        <div style={{ display: "flex", flexDirection: "column", gap: "4px" }}>
          <Typography level="h3">Heading</Typography>
          <Typography level="body-1">Body text</Typography>
        </div>
      </>
    ),
  },
  {
    title: "Chip",
    href: "@design-systems∕apollo-ui/Components/Data Display/Chip",
    description: "Compact element for tags, filters, or selections",
    keywords: ["chip", "tag", "filter", "selection", "removable"],
    component: (
      <>
        <div style={{ display: "flex", gap: "8px", flexWrap: "wrap" }}>
          <Chip label="Tag 1" />
          <Chip label="Removable" onDelete={() => {}} />
        </div>
      </>
    ),
  },
  {
    title: "Sorting Icon",
    href: "@design-systems∕apollo-ui/Components/Data Display/SortingIcon",
    description: "Icon indicating sort direction in tables",
    keywords: ["sorting", "icon", "table", "direction", "asc", "desc"],
    component: (
      <>
        <div style={{ display: "flex", gap: "16px", alignItems: "center" }}>
          <SortingIcon status="asc" />
          <SortingIcon status="desc" />
          <SortingIcon />
        </div>
      </>
    ),
  },
  {
    title: "Product Card",
    href: "@design-systems∕apollo-ui/Components/Utilities/ProductCard",
    description: "Card component for displaying product information",
    keywords: ["product", "card", "display", "image", "title", "pricing"],
    component: (
      <>
        <div style={{ width: "150px" }}>
          <ProductCard
            title="Sample Product"
            imageSrc="https://picsum.photos/150/150?random=1"
          />
        </div>
      </>
    ),
  },

  // NAVIGATION CATEGORY
  {
    title: "Breadcrumbs",
    href: "@design-systems∕apollo-ui/Components/Navigation/Breadcrumbs",
    description: "Navigation trail showing current page location",
    keywords: ["breadcrumbs", "navigation", "trail", "path", "hierarchy"],
    component: (
      <>
        <Breadcrumbs>
          <Typography level="body-1">Home</Typography>
          <Typography level="body-1">Category</Typography>
          <Typography level="body-1">Current</Typography>
        </Breadcrumbs>
      </>
    ),
  },
  {
    title: "Pagination",
    href: "@design-systems∕apollo-ui/Components/Navigation/Pagination",
    description: "Navigation control for paginated content",
    keywords: ["pagination", "navigation", "pages", "next", "previous"],
    component: (
      <>
        <Pagination count={5} defaultPage={2} onChange={() => {}} />
      </>
    ),
  },
  {
    title: "Sidebar",
    href: "@design-systems∕apollo-ui/Components/Navigation/Sidebar",
    description: "Vertical navigation component with collapsible menu items",
    keywords: ["sidebar", "navigation", "vertical", "menu", "collapsible"],
    component: (
      <>
        <Sidebar
          title="Admin Dashboard"
          menus={[
            {
              key: "dashboard",
              label: "Dashboard",
            },
            {
              key: "ecommerce",
              label: "E-commerce",
              children: [
                { key: "products", label: "Products" },
                { key: "orders", label: "Orders" },
              ],
            },
          ]}
          selectedMenuKey="dashboard"
          onSelectMenu={() => {}}
        />
      </>
    ),
  },
  {
    title: "Navbar",
    href: "@design-systems∕apollo-ui/Components/Navigation/Navbar",
    description: "Horizontal navigation bar for top-level menu items",
    keywords: ["navbar", "navigation", "horizontal", "menu", "top-level"],
    component: (
      <>
        <NavBar
          menu={[
            { key: "home", label: "Home", icon: <Home /> },
            { key: "shop", label: "Shop" , icon: <Shop /> },
            { key: "orders", label: "Orders", icon: <Shopping /> },
            { key: "profile", label: "Profile", icon: <User /> },
          ]}
          activeIndex={0}
          onChange={() => {}}
        />
      </>
    ),
  },

    // LAYOUT CATEGORY
  {
    title: "Accordion",
    href: "@design-systems∕apollo-ui/Components/Layout/Accordion",
    description: "Collapsible content sections",
    keywords: ["accordion", "collapsible", "expand", "collapse", "sections"],
    component: (
      <>
        <div style={{ width: "200px" }}>
          <Accordion header="Section 1">
            <Typography level="body-1">Content for section 1</Typography>
          </Accordion>
        </div>
      </>
    ),
  },
  {
    title: "Tabs",
    href: "@design-systems∕apollo-ui/Components/Navigation/Tabs",
    description: "Tabbed interface for organizing content sections",
    keywords: ["tabs", "navigation", "sections", "content", "switch"],
    component: (
      <>
        <div style={{ width: "200px" }}>
          <Tabs defaultValue="tab1">
            <TabsList>
              <Tab value="tab1">Tab 1</Tab>
              <Tab value="tab2">Tab 2</Tab>
            </TabsList>
            <TabPanel value="tab1">Content 1</TabPanel>
            <TabPanel value="tab2">Content 2</TabPanel>
          </Tabs>
        </div>
      </>
    ),
  },
  {
    title: "Capsule Tab",
    href: "@design-systems∕apollo-ui/Components/Navigation/CapsuleTab",
    description: "Pill-style tab component for compact navigation",
    keywords: ["capsule", "tab", "pill", "navigation", "compact"],
    component: (
      <>
        <CapsuleTab
          tabs={[
            { id: "tab1", label: "Active" },
            { id: "tab2", label: "Inactive" },
          ]}
          selectedIndex={0}
          onSelect={() => {}}
        />
      </>
    ),
  },

  // FEEDBACK CATEGORY
  {
    title: "Alert",
    href: "@design-systems∕apollo-ui/Components/Feedback/Alert",
    description: "Notification component for important messages",
    keywords: [
      "alert",
      "notification",
      "message",
      "warning",
      "error",
      "success",
    ],
    component: (
      <>
        <Alert
          color="info"
          title="Information"
          description="This is an information alert"
        />
      </>
    ),
  },
  {
    title: "Toast",
    href: "@design-systems∕apollo-ui/Components/Feedback/Toast",
    description: "Temporary notification messages",
    keywords: ["toast", "notification", "message", "temporary", "snackbar"],
    component: <Alert title="Hello World" description="This is a toast" />,
  },
  {
    title: "Modal",
    href: "@design-systems∕apollo-ui/Components/Feedback/Modal",
    description: "Dialog overlay for important content and actions",
    keywords: ["modal", "dialog", "overlay", "popup", "confirmation"],
    component: null, // Modal requires complex state management for demo
  },

]

export default legacyComponents
